#!/usr/bin/env python3
"""
本地模型测试脚本
用于验证Open Deep Research项目的本地模型配置是否正确
"""

import asyncio
import os
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from open_deep_research.local_model_config import get_model_config_for_langchain, get_configurable_model
from open_deep_research.configuration import Configuration
from langchain.chat_models import init_chat_model
from langchain_core.messages import HumanMessage


async def test_model_connection(model_string: str, test_message: str = "Hello, how are you?"):
    """测试模型连接"""
    print(f"\n🧪 测试模型: {model_string}")
    
    try:
        # 获取模型配置
        config = get_model_config_for_langchain(model_string)
        print(f"📋 模型配置: {config}")
        
        # 初始化模型
        model = init_chat_model(
            model=config.get("model", model_string),
            model_provider=config.get("provider", "openai"),
            api_key=config.get("api_key"),
            base_url=config.get("base_url"),
            max_tokens=100
        )
        
        # 发送测试消息
        print(f"📤 发送测试消息: {test_message}")
        response = await model.ainvoke([HumanMessage(content=test_message)])
        print(f"✅ 模型响应: {response.content[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型测试失败: {str(e)}")
        return False


async def test_configuration():
    """测试配置系统"""
    print("\n🔧 测试配置系统...")
    
    try:
        config = Configuration()
        print(f"✅ 默认配置加载成功")
        print(f"   - 摘要模型: {config.summarization_model}")
        print(f"   - 研究模型: {config.research_model}")
        print(f"   - 压缩模型: {config.compression_model}")
        print(f"   - 最终报告模型: {config.final_report_model}")
        return True
        
    except Exception as e:
        print(f"❌ 配置系统测试失败: {str(e)}")
        return False


async def test_environment_variables():
    """测试环境变量配置"""
    print("\n🌍 检查环境变量...")
    
    required_vars = [
        "OPENAI_API_KEY",
        "OPENAI_API_BASE", 
        "THINKING_API_KEY",
        "THINKING_API_BASE"
    ]
    
    all_good = True
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {value}")
        else:
            print(f"❌ {var}: 未设置")
            all_good = False
    
    return all_good


async def test_api_endpoints():
    """测试API端点连接"""
    print("\n🌐 测试API端点连接...")
    
    import aiohttp
    
    endpoints = [
        ("Instruct模型", os.getenv("OPENAI_API_BASE", "http://192.168.100.182:8002/v1")),
        ("Thinking模型", os.getenv("THINKING_API_BASE", "http://192.168.100.182:8003/v1"))
    ]
    
    all_good = True
    for name, url in endpoints:
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{url}/models", timeout=5) as response:
                    if response.status == 200:
                        print(f"✅ {name} ({url}): 连接成功")
                    else:
                        print(f"⚠️ {name} ({url}): HTTP {response.status}")
                        all_good = False
        except Exception as e:
            print(f"❌ {name} ({url}): 连接失败 - {str(e)}")
            all_good = False
    
    return all_good


async def main():
    """主测试函数"""
    print("🚀 Open Deep Research 本地模型配置测试")
    print("=" * 60)
    
    # 测试环境变量
    env_ok = await test_environment_variables()
    
    # 测试API端点
    api_ok = await test_api_endpoints()
    
    # 测试配置系统
    config_ok = await test_configuration()
    
    # 测试模型连接
    models_to_test = [
        "openai:Qwen3-30B-A3B-Instruct-2507",
        "thinking:Qwen3-30B-A3B-Thinking-2507"
    ]
    
    model_results = []
    for model in models_to_test:
        result = await test_model_connection(model, "请简单介绍一下你自己")
        model_results.append(result)
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   🌍 环境变量: {'✅ 通过' if env_ok else '❌ 失败'}")
    print(f"   🌐 API端点: {'✅ 通过' if api_ok else '❌ 失败'}")
    print(f"   🔧 配置系统: {'✅ 通过' if config_ok else '❌ 失败'}")
    print(f"   🤖 模型连接: {'✅ 通过' if all(model_results) else '❌ 失败'}")
    
    if all([env_ok, api_ok, config_ok] + model_results):
        print("\n🎉 所有测试通过！您的本地模型配置已就绪。")
        print("\n🚀 现在可以启动项目:")
        print("   langgraph dev")
        return True
    else:
        print("\n⚠️ 部分测试失败，请检查配置。")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

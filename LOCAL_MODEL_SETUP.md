# Open Deep Research 本地模型配置指南

本指南将帮助您将Open Deep Research项目配置为使用您的本地Qwen模型。

## 📋 配置概览

您的本地模型配置：
- **Instruct模型**: `Qwen3-30B-A3B-Instruct-2507` (用于一般任务)
  - API地址: `http://***************:8002/v1`
- **Thinking模型**: `Qwen3-30B-A3B-Thinking-2507` (用于复杂思考任务)
  - API地址: `http://***************:8003/v1`

## 🔧 已完成的修改

### 1. 环境变量配置 (`.env`)
```bash
# 本地模型配置
OPENAI_API_KEY=dummy_key_for_local_model
OPENAI_API_BASE=http://***************:8002/v1

THINKING_API_KEY=dummy_key_for_thinking_model
THINKING_API_BASE=http://***************:8003/v1

# 搜索API配置
TAVILY_API_KEY=your_tavily_api_key_here
```

### 2. 主配置文件修改
- `src/open_deep_research/configuration.py`: 更新默认模型配置
- `src/legacy/configuration.py`: 更新legacy版本配置

### 3. 模型分工策略
- **摘要任务**: Instruct模型 (快速、高效)
- **研究任务**: Instruct模型 (信息收集和分析)
- **压缩任务**: Instruct模型 (结果整理)
- **最终报告**: Thinking模型 (深度思考和综合)

### 4. 新增支持文件
- `src/open_deep_research/local_model_config.py`: 本地模型配置支持
- `test_local_models.py`: 配置验证脚本

## 🚀 快速启动步骤

### 步骤1: 验证配置
```bash
# 运行配置验证脚本
python test_local_models.py
```

### 步骤2: 安装依赖
```bash
# 安装项目依赖
pip install -e .

# 如果需要Tavily搜索功能，请设置API密钥
# 获取免费API密钥: https://tavily.com/
```

### 步骤3: 启动项目
```bash
# 启动LangGraph开发服务器
langgraph dev
```

### 步骤4: 访问界面
- 打开浏览器访问: `http://localhost:8123`
- 在LangGraph Studio中测试您的研究助手

## 🧪 测试验证

### 基础连接测试
```python
# 测试Instruct模型
curl -X POST http://***************:8002/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "Qwen3-30B-A3B-Instruct-2507",
    "messages": [{"role": "user", "content": "Hello"}],
    "max_tokens": 100
  }'

# 测试Thinking模型
curl -X POST http://***************:8003/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "Qwen3-30B-A3B-Thinking-2507", 
    "messages": [{"role": "user", "content": "Hello"}],
    "max_tokens": 100
  }'
```

### 功能测试
1. **简单查询**: "请介绍一下人工智能的发展历史"
2. **复杂研究**: "分析当前大语言模型的技术趋势和发展方向"
3. **多轮对话**: 测试澄清问题功能

## 🔍 故障排除

### 常见问题

#### 1. 模型连接失败
- 检查API服务是否正常运行
- 验证IP地址和端口是否正确
- 确认防火墙设置允许连接

#### 2. 环境变量未生效
```bash
# 重新加载环境变量
source .env

# 或者重启终端
```

#### 3. 依赖安装问题
```bash
# 清理并重新安装
pip uninstall open-deep-research
pip install -e .
```

#### 4. LangGraph启动失败
```bash
# 检查Python版本 (需要3.11+)
python --version

# 检查LangGraph安装
pip show langgraph
```

### 调试技巧

#### 启用详细日志
```bash
# 设置环境变量启用调试
export LANGSMITH_TRACING=true
export LANGSMITH_PROJECT=open_deep_research_debug
```

#### 检查模型调用
```python
# 在代码中添加调试信息
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📊 性能优化建议

### 1. 并发控制
- 根据硬件性能调整 `max_concurrent_research_units`
- 默认值为5，可根据需要调整为2-10

### 2. Token限制
- 监控模型的token使用情况
- 适当调整各模型的 `max_tokens` 配置

### 3. 搜索API选择
- 推荐使用Tavily进行网络搜索
- 如果不需要实时信息，可以设置为"none"

## 🔄 配置更新

如需修改模型配置，可以通过以下方式：

### 1. 环境变量方式
```bash
export RESEARCH_MODEL="openai:Qwen3-30B-A3B-Instruct-2507"
export FINAL_REPORT_MODEL="thinking:Qwen3-30B-A3B-Thinking-2507"
```

### 2. 配置文件方式
直接修改 `src/open_deep_research/configuration.py` 中的默认值

### 3. 运行时配置
在LangGraph Studio中动态调整配置参数

## 📞 技术支持

如果遇到问题，请检查：
1. 本地模型服务是否正常运行
2. 网络连接是否正常
3. 环境变量是否正确设置
4. 依赖包是否完整安装

祝您使用愉快！🎉

{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-01T01:07:46.660Z", "updatedAt": "2025-08-01T01:07:46.668Z", "resourceCount": 3}, "resources": [{"id": "deep-research-mentor", "source": "project", "protocol": "role", "name": "Deep Research Mentor 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/deep-research-mentor/deep-research-mentor.role.md", "metadata": {"createdAt": "2025-08-01T01:07:46.665Z", "updatedAt": "2025-08-01T01:07:46.665Z", "scannedAt": "2025-08-01T01:07:46.665Z", "path": "role/deep-research-mentor/deep-research-mentor.role.md"}}, {"id": "mentoring-methodology", "source": "project", "protocol": "execution", "name": "Mentoring Methodology 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/deep-research-mentor/execution/mentoring-methodology.execution.md", "metadata": {"createdAt": "2025-08-01T01:07:46.666Z", "updatedAt": "2025-08-01T01:07:46.666Z", "scannedAt": "2025-08-01T01:07:46.666Z", "path": "role/deep-research-mentor/execution/mentoring-methodology.execution.md"}}, {"id": "deep-research-thinking", "source": "project", "protocol": "thought", "name": "Deep Research Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/deep-research-mentor/thought/deep-research-thinking.thought.md", "metadata": {"createdAt": "2025-08-01T01:07:46.667Z", "updatedAt": "2025-08-01T01:07:46.667Z", "scannedAt": "2025-08-01T01:07:46.667Z", "path": "role/deep-research-mentor/thought/deep-research-thinking.thought.md"}}], "stats": {"totalResources": 3, "byProtocol": {"role": 1, "execution": 1, "thought": 1}, "bySource": {"project": 3}}}
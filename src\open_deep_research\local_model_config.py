"""
本地模型配置支持模块
用于处理自定义的本地模型提供商配置
"""
import os
from typing import Dict, Any, Optional
from langchain.chat_models import init_chat_model
from langchain_core.language_models import BaseChatModel


def get_local_model_config(model_name: str, provider: str) -> Dict[str, Any]:
    """
    获取本地模型的配置参数
    
    Args:
        model_name: 模型名称
        provider: 提供商名称 (openai, thinking)
        
    Returns:
        模型配置字典
    """
    config = {}
    
    if provider == "openai":
        # 使用Instruct模型的配置
        config.update({
            "api_key": os.getenv("OPENAI_API_KEY", "dummy_key_for_local_model"),
            "base_url": os.getenv("OPENAI_API_BASE", "http://192.168.100.182:8002/v1"),
        })
    elif provider == "thinking":
        # 使用Thinking模型的配置
        config.update({
            "api_key": os.getenv("THINKING_API_KEY", "dummy_key_for_thinking_model"),
            "base_url": os.getenv("THINKING_API_BASE", "http://192.168.100.182:8003/v1"),
        })
    
    return config


def init_local_chat_model(
    model: str,
    provider: Optional[str] = None,
    **kwargs
) -> BaseChatModel:
    """
    初始化本地聊天模型
    
    Args:
        model: 模型名称，格式为 "provider:model_name" 或直接模型名
        provider: 可选的提供商名称
        **kwargs: 其他参数
        
    Returns:
        初始化的聊天模型
    """
    # 解析模型字符串
    if ":" in model:
        provider_name, model_name = model.split(":", 1)
    else:
        provider_name = provider or "openai"
        model_name = model
    
    # 获取本地模型配置
    local_config = get_local_model_config(model_name, provider_name)
    
    # 合并配置
    final_kwargs = {**local_config, **kwargs}
    
    # 对于thinking模型，我们使用openai provider但配置不同的base_url
    if provider_name == "thinking":
        provider_name = "openai"
    
    # 初始化模型
    return init_chat_model(
        model=model_name,
        model_provider=provider_name,
        **final_kwargs
    )


def patch_model_initialization():
    """
    修补模型初始化函数以支持本地模型
    """
    # 这个函数可以用来在运行时修补init_chat_model函数
    # 如果需要更深度的集成，可以在这里实现
    pass


# 模型映射配置
LOCAL_MODEL_MAPPING = {
    "openai:Qwen3-30B-A3B-Instruct-2507": {
        "provider": "openai",
        "model": "Qwen3-30B-A3B-Instruct-2507",
        "base_url": "http://192.168.100.182:8002/v1",
        "api_key": "dummy_key_for_local_model"
    },
    "thinking:Qwen3-30B-A3B-Thinking-2507": {
        "provider": "openai",  # 使用OpenAI兼容接口
        "model": "Qwen3-30B-A3B-Thinking-2507", 
        "base_url": "http://192.168.100.182:8003/v1",
        "api_key": "dummy_key_for_thinking_model"
    }
}


def get_model_config_for_langchain(model_string: str) -> Dict[str, Any]:
    """
    为LangChain获取模型配置
    
    Args:
        model_string: 模型字符串，如 "openai:Qwen3-30B-A3B-Instruct-2507"
        
    Returns:
        LangChain兼容的配置字典
    """
    if model_string in LOCAL_MODEL_MAPPING:
        config = LOCAL_MODEL_MAPPING[model_string].copy()
        return config
    
    # 如果不在映射中，尝试解析
    if ":" in model_string:
        provider, model_name = model_string.split(":", 1)
        return get_local_model_config(model_name, provider)
    
    # 默认配置
    return {
        "provider": "openai",
        "model": model_string,
        "base_url": "http://192.168.100.182:8002/v1",
        "api_key": "dummy_key_for_local_model"
    }

<?xml version="1.0" encoding="UTF-8"?>
<memory>
  <item id="mem_1754010647259_zn9firb93" time="2025/08/01 01:10">
    <content>
      LangChain核心架构知识：
      1. Runnable接口是LangChain的核心抽象，支持invoke、batch、stream方法
      2. LCEL（LangChain Expression Language）支持声明式链式组合
      3. Agent系统包含AgentExecutor、Tools、Memory等核心组件
      4. 支持多种模型提供商（OpenAI、Anthropic、Google等）
      5. 内置工具生态系统，支持搜索、数据库、API集成
      6. 记忆管理包括ConversationBufferMemory、ReadOnlySharedMemory等
      7. 支持异步操作和并发处理
    </content>
    <tags>#工具使用</tags>
  </item>
  <item id="mem_1754010657005_wcsougub9" time="2025/08/01 01:10">
    <content>
      LangGraph状态图工作流核心知识：
      1. StateGraph是构建多智能体系统的核心类，支持状态管理和节点间转换
      2. Command/Send API实现智能体间通信和状态更新
      3. Checkpointer机制提供持久化内存（MemorySaver、PostgresSaver等）
      4. 支持多层嵌套图结构，如supervisor_subgraph嵌套在主图中
      5. 多智能体架构支持并发处理和动态handoff
      6. 内置工具节点（ToolNode）和条件边（conditional_edges）
      7. 支持人机交互循环（human-in-the-loop）和长期记忆
    </content>
    <tags>#工具使用</tags>
  </item>
  <item id="mem_1754010666916_yt6n00en8" time="2025/08/01 01:11">
    <content>
      FastAPI现代Web框架核心知识：
      1. 基于Python类型提示的自动API文档生成和数据验证
      2. 依赖注入系统（Depends）支持复杂的依赖关系图
      3. 异步支持（async/await）实现高性能并发处理
      4. APIRouter模块化路由管理，支持prefix、tags、dependencies配置
      5. 中间件系统支持请求/响应处理和ASGI兼容
      6. 内置安全特性（OAuth2、JWT、HTTPS）
      7. 支持WebSocket、背景任务（BackgroundTasks）和流式响应
    </content>
    <tags>#其他</tags>
  </item>
</memory>